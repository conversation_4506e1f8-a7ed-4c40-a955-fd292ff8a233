{"timestamp": "2025-06-01T06:42:14.191Z", "summary": {"total": 32, "used": 24, "unused": 8, "suspicious": 4, "usageRate": "75.0"}, "routes": [{"path": "/api/admin/blogs/:id", "file": "app/api/admin/blogs/[id]/route.ts", "methods": ["GET", "PUT", "DELETE"], "used": true, "suspicious": false, "usages": 2, "usageFiles": ["app/admin/blogs/[id]/edit/page.tsx", "app/admin/blogs/page.tsx"]}, {"path": "/api/admin/blogs", "file": "app/api/admin/blogs/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": false, "usages": 4, "usageFiles": ["app/admin/blogs/[id]/edit/page.tsx", "app/admin/blogs/new/page.tsx", "app/admin/blogs/page.tsx", "app/admin/page.tsx"]}, {"path": "/api/admin/create-admin", "file": "app/api/admin/create-admin/route.ts", "methods": ["POST"], "used": false, "suspicious": false, "usages": 0, "usageFiles": []}, {"path": "/api/admin/inquiries/:id", "file": "app/api/admin/inquiries/[id]/route.ts", "methods": ["GET", "PUT"], "used": true, "suspicious": false, "usages": 3, "usageFiles": ["app/admin/inquiries/[id]/components/InquiryDetail.tsx", "app/admin/inquiries/[id]/components/InquiryDetailClient.tsx", "app/admin/inquiries/page.tsx"]}, {"path": "/api/admin/inquiries/export", "file": "app/api/admin/inquiries/export/route.ts", "methods": ["GET"], "used": false, "suspicious": false, "usages": 0, "usageFiles": []}, {"path": "/api/admin/inquiries", "file": "app/api/admin/inquiries/route.ts", "methods": ["GET", "PUT"], "used": true, "suspicious": false, "usages": 4, "usageFiles": ["app/admin/inquiries/[id]/components/InquiryDetail.tsx", "app/admin/inquiries/[id]/components/InquiryDetailClient.tsx", "app/admin/inquiries/page.tsx", "app/admin/page.tsx"]}, {"path": "/api/admin/roles", "file": "app/api/admin/roles/route.ts", "methods": ["GET"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["components/admin/AdminUserManagement.tsx"]}, {"path": "/api/admin/team-members/:id", "file": "app/api/admin/team-members/[id]/route.ts", "methods": ["GET", "PUT", "DELETE"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["app/admin/team-members/page.tsx"]}, {"path": "/api/admin/team-members", "file": "app/api/admin/team-members/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["app/admin/team-members/page.tsx"]}, {"path": "/api/admin/trips/:id", "file": "app/api/admin/trips/[id]/route.ts", "methods": ["GET", "PUT", "DELETE"], "used": true, "suspicious": false, "usages": 2, "usageFiles": ["app/admin/trips/[id]/edit/page.tsx", "app/admin/trips/page.tsx"]}, {"path": "/api/admin/trips", "file": "app/api/admin/trips/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": false, "usages": 10, "usageFiles": ["app/admin/page.tsx", "app/admin/trips/[id]/edit/page.tsx", "app/admin/trips/new/page.tsx", "app/admin/trips/page.tsx", "app/admin/trips-photos/[id]/components/EditTripPhotoContent.tsx", "app/admin/trips-photos/[id]/components/TripPhotoDetailContent.tsx", "app/admin/trips-photos/components/TripPhotoUploader.tsx", "app/admin/trips-photos/components/TripPhotosManager.tsx", "app/admin/trips-photos/new/page.tsx", "app/admin/trips-photos/page.tsx"]}, {"path": "/api/admin/trips-photos/:id", "file": "app/api/admin/trips-photos/[id]/route.ts", "methods": ["GET", "PUT", "DELETE"], "used": true, "suspicious": false, "usages": 5, "usageFiles": ["app/admin/trips-photos/[id]/components/EditTripPhotoContent.tsx", "app/admin/trips-photos/[id]/components/TripPhotoDetailContent.tsx", "app/admin/trips-photos/components/TripPhotoUploader.tsx", "app/admin/trips-photos/components/TripPhotosManager.tsx", "app/admin/trips-photos/page.tsx"]}, {"path": "/api/admin/trips-photos", "file": "app/api/admin/trips-photos/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": false, "usages": 6, "usageFiles": ["app/admin/trips-photos/[id]/components/EditTripPhotoContent.tsx", "app/admin/trips-photos/[id]/components/TripPhotoDetailContent.tsx", "app/admin/trips-photos/components/TripPhotoUploader.tsx", "app/admin/trips-photos/components/TripPhotosManager.tsx", "app/admin/trips-photos/new/page.tsx", "app/admin/trips-photos/page.tsx"]}, {"path": "/api/admin/trips-photos/upload-to-drive", "file": "app/api/admin/trips-photos/upload-to-drive/route.ts", "methods": ["POST"], "used": false, "suspicious": false, "usages": 0, "usageFiles": []}, {"path": "/api/admin/trips-photos/validate-folder", "file": "app/api/admin/trips-photos/validate-folder/route.ts", "methods": ["POST"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["app/admin/trips-photos/components/TripPhotoUploader.tsx"]}, {"path": "/api/admin/trips-photos/watermark", "file": "app/api/admin/trips-photos/watermark/route.ts", "methods": ["POST"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["app/admin/trips-photos/components/TripPhotoUploader.tsx"]}, {"path": "/api/admin/users/:id", "file": "app/api/admin/users/[id]/route.ts", "methods": ["PUT", "DELETE"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["components/admin/AdminUserManagement.tsx"]}, {"path": "/api/admin/users", "file": "app/api/admin/users/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": false, "usages": 2, "usageFiles": ["app/admin/page.tsx", "components/admin/AdminUserManagement.tsx"]}, {"path": "/api/auth/create-admin", "file": "app/api/auth/create-admin/route.ts", "methods": ["POST"], "used": false, "suspicious": false, "usages": 0, "usageFiles": []}, {"path": "/api/auth/login", "file": "app/api/auth/login/route.ts", "methods": ["POST"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["lib/hooks/useAuth.ts"]}, {"path": "/api/auth/logout", "file": "app/api/auth/logout/route.ts", "methods": ["POST"], "used": true, "suspicious": false, "usages": 2, "usageFiles": ["components/layout/AdminSidebar.tsx", "lib/hooks/useAuth.ts"]}, {"path": "/api/auth/user", "file": "app/api/auth/user/route.ts", "methods": ["GET"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["lib/hooks/useAuth.ts"]}, {"path": "/api/blog", "file": "app/api/blog/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": true, "usages": 4, "usageFiles": ["app/admin/blogs/[id]/edit/page.tsx", "app/admin/blogs/new/page.tsx", "app/admin/blogs/page.tsx", "app/admin/page.tsx"]}, {"path": "/api/cloudinary/sign", "file": "app/api/cloudinary/sign/route.ts", "methods": ["POST"], "used": false, "suspicious": false, "usages": 0, "usageFiles": []}, {"path": "/api/cloudinary/upload", "file": "app/api/cloudinary/upload/route.ts", "methods": ["POST"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["components/ui/CloudinaryUpload.tsx"]}, {"path": "/api/cron/trip-deactivation", "file": "app/api/cron/trip-deactivation/route.ts", "methods": ["GET"], "used": false, "suspicious": false, "usages": 0, "usageFiles": []}, {"path": "/api/inquiries", "file": "app/api/inquiries/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": false, "usages": 6, "usageFiles": ["app/admin/inquiries/[id]/components/InquiryDetail.tsx", "app/admin/inquiries/[id]/components/InquiryDetailClient.tsx", "app/admin/inquiries/page.tsx", "app/admin/page.tsx", "components/contact/ContactForm.tsx", "components/sections/ContactSection.tsx"]}, {"path": "/api/newsletter", "file": "app/api/newsletter/route.ts", "methods": ["GET", "POST", "DELETE"], "used": true, "suspicious": false, "usages": 1, "usageFiles": ["components/layout/Footer.tsx"]}, {"path": "/api/testimonials", "file": "app/api/testimonials/route.ts", "methods": ["GET", "POST"], "used": false, "suspicious": false, "usages": 0, "usageFiles": []}, {"path": "/api/trips/:id/images", "file": "app/api/trips/[id]/images/route.ts", "methods": ["GET", "POST", "PUT"], "used": false, "suspicious": true, "usages": 0, "usageFiles": []}, {"path": "/api/trips/:id", "file": "app/api/trips/[id]/route.ts", "methods": ["GET", "PUT", "DELETE"], "used": true, "suspicious": true, "usages": 2, "usageFiles": ["app/admin/trips/[id]/edit/page.tsx", "app/admin/trips/page.tsx"]}, {"path": "/api/trips", "file": "app/api/trips/route.ts", "methods": ["GET", "POST"], "used": true, "suspicious": true, "usages": 10, "usageFiles": ["app/admin/page.tsx", "app/admin/trips/[id]/edit/page.tsx", "app/admin/trips/new/page.tsx", "app/admin/trips/page.tsx", "app/admin/trips-photos/[id]/components/EditTripPhotoContent.tsx", "app/admin/trips-photos/[id]/components/TripPhotoDetailContent.tsx", "app/admin/trips-photos/components/TripPhotoUploader.tsx", "app/admin/trips-photos/components/TripPhotosManager.tsx", "app/admin/trips-photos/new/page.tsx", "app/admin/trips-photos/page.tsx"]}], "recommendations": [{"type": "removal", "priority": "high", "description": "Remove 8 unused API routes", "routes": ["/api/admin/create-admin", "/api/admin/inquiries/export", "/api/admin/trips-photos/upload-to-drive", "/api/auth/create-admin", "/api/cloudinary/sign", "/api/cron/trip-deactivation", "/api/testimonials", "/api/trips/:id/images"]}, {"type": "review", "priority": "medium", "description": "Review 4 suspicious routes with placeholder responses", "routes": ["/api/blog", "/api/trips/:id/images", "/api/trips/:id", "/api/trips"]}]}