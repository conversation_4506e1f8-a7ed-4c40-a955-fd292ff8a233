import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import TripsPhotosClient from '@/components/trips-photos/TripsPhotosClient'
import { createServerSupabase } from '@/lib/supabase-server'
import { TripPhotoDetails } from '@/types/trip-photos'

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic'
export const revalidate = 0

export const metadata: Metadata = {
  title: 'Trips Photos - Positive7 Educational Tours',
  description: 'Browse and download photos from our recent educational trips and adventures. Relive the memories and experiences from our amazing journeys.',
  keywords: 'trip photos, educational tour photos, student trip memories, Positive7 gallery, travel photos'
}

// Fetch trip photo albums from the database
async function getTripPhotoAlbums() {
  const supabase = createServerSupabase()
  
  const { data: tripPhotoAlbums, error } = await supabase
    .from('trip_photos_details')
    .select('*')
    .order('created_at', { ascending: false })
    
  if (error) {
    console.error('Error fetching trip photo albums:', error)
    return []
  }
  
  // Transform the data to match the client component's expected format
  return tripPhotoAlbums.map((rawAlbum) => {
    // Convert raw data to properly typed TripPhotoDetails
    const album: TripPhotoDetails = {
      ...rawAlbum,
      created_at: rawAlbum.created_at || new Date().toISOString(),
      updated_at: rawAlbum.updated_at || new Date().toISOString()
    };
    
    return {
      id: album.id,
      title: album.trip_name,
      date: new Date(album.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      coverImage: album.featured_image_url || 'https://res.cloudinary.com/peebst3r/image/upload/v1748754487/positive7/trips/Manali-River.jpg', // Default image if none provided
      photoCount: 0, // We don't store this in the database yet
      downloadLink: album.google_drive_link || '#',
      location: album.trip_description?.split(',')[0] || 'Various Locations', // Use first part of description as location
      participants: album.trip_description || 'Various Schools',
      password: album.access_password || null
    };
  });
}

export default async function TripsPhotosPage() {
  const albums = await getTripPhotoAlbums()
  
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <TripsPhotosClient albums={albums} />
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
