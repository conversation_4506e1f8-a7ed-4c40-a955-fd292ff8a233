#!/usr/bin/env node

/**
 * Comprehensive Functionality Test Script
 * Tests all APIs, database connections, and external services
 * Run this script manually to check system health
 */

const { createClient } = require('@supabase/supabase-js');
const { google } = require('googleapis');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Test configuration
const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const TIMEOUT = 10000; // 10 seconds

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'bold');
  console.log('='.repeat(60));
}

function logTest(name, status, details = '') {
  const icon = status ? '✅' : '❌';
  const color = status ? 'green' : 'red';
  log(`${icon} ${name}`, color);
  if (details) {
    log(`   ${details}`, 'yellow');
  }
}

// Test results tracking
const results = {
  total: 0,
  passed: 0,
  failed: 0,
  tests: []
};

function recordTest(name, status, details = '') {
  results.total++;
  if (status) {
    results.passed++;
  } else {
    results.failed++;
  }
  results.tests.push({ name, status, details });
  logTest(name, status, details);
}

// Test functions
async function testEnvironmentVariables() {
  logSection('🔧 Environment Variables');
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'SUPABASE_JWT_SECRET',
    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
    'NEXT_PUBLIC_CLOUDINARY_API_KEY',
    'CLOUDINARY_API_SECRET',
    'GOOGLE_CLIENT_EMAIL',
    'GOOGLE_PRIVATE_KEY',
    'EMAIL_USER',
    'EMAIL_PASSWORD'
  ];

  for (const varName of requiredVars) {
    const value = process.env[varName];
    recordTest(
      `${varName}`,
      !!value,
      value ? 'Set' : 'Missing'
    );
  }
}

async function testSupabaseConnection() {
  logSection('🗄️ Supabase Database');
  
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Test basic connection
    const { data, error } = await supabase.from('trips').select('count').limit(1);
    recordTest('Database Connection', !error, error?.message);

    // Test each table
    const tables = [
      'trips',
      'blog_posts',
      'trip_photos_details',
      'team_members',
      'inquiries'
    ];

    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1);
        recordTest(`Table: ${table}`, !error, error?.message);
      } catch (err) {
        recordTest(`Table: ${table}`, false, err.message);
      }
    }

  } catch (error) {
    recordTest('Supabase Setup', false, error.message);
  }
}

async function testGoogleDriveAPI() {
  logSection('🔗 Google Drive API');
  
  try {
    const credentials = {
      client_email: process.env.GOOGLE_CLIENT_EMAIL,
      private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    };

    if (!credentials.client_email || !credentials.private_key) {
      recordTest('Google Credentials', false, 'Missing credentials');
      return;
    }

    recordTest('Google Credentials', true, 'Present');

    const auth = new google.auth.JWT({
      email: credentials.client_email,
      key: credentials.private_key,
      scopes: ['https://www.googleapis.com/auth/drive'],
    });

    await auth.authorize();
    recordTest('Google Authentication', true, 'Successful');

    const drive = google.drive({ version: 'v3', auth });
    const response = await drive.about.get({ fields: 'user' });
    recordTest('Google Drive Access', true, `User: ${response.data.user?.emailAddress}`);

  } catch (error) {
    recordTest('Google Drive API', false, error.message);
  }
}

async function testCloudinaryAPI() {
  logSection('☁️ Cloudinary API');
  
  try {
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    recordTest('Cloudinary Credentials', !!(cloudName && apiKey && apiSecret));

    // Test Cloudinary API endpoint
    const testUrl = `https://api.cloudinary.com/v1_1/${cloudName}/resources/image`;
    const auth = Buffer.from(`${apiKey}:${apiSecret}`).toString('base64');
    
    const response = await fetch(testUrl, {
      headers: { 'Authorization': `Basic ${auth}` },
      timeout: TIMEOUT
    });

    recordTest('Cloudinary API Access', response.ok, `Status: ${response.status}`);

  } catch (error) {
    recordTest('Cloudinary API', false, error.message);
  }
}

async function testAPIEndpoints() {
  logSection('🌐 API Endpoints');
  
  const endpoints = [
    { path: '/api/trips', method: 'GET' },
    { path: '/api/blog', method: 'GET' },
    { path: '/api/inquiries', method: 'GET' },
    { path: '/api/admin/trips', method: 'GET' },
    { path: '/api/admin/blogs', method: 'GET' },
    { path: '/api/admin/trips-photos', method: 'GET' },
    { path: '/api/admin/team-members', method: 'GET' },
    { path: '/api/admin/inquiries', method: 'GET' },
    { path: '/api/cloudinary/upload', method: 'POST' }
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint.path}`, {
        method: endpoint.method,
        timeout: TIMEOUT,
        headers: endpoint.method === 'POST' ? { 'Content-Type': 'application/json' } : {}
      });

      const isSuccess = response.status < 500; // Allow 4xx but not 5xx
      recordTest(
        `${endpoint.method} ${endpoint.path}`,
        isSuccess,
        `Status: ${response.status}`
      );

    } catch (error) {
      recordTest(`${endpoint.method} ${endpoint.path}`, false, error.message);
    }
  }
}

async function testPageRoutes() {
  logSection('📄 Page Routes');
  
  const routes = [
    '/',
    '/about',
    '/trips',
    '/blog',
    '/trips-photos',
    '/contact',
    '/rural-initiative',
    '/gallery',
    '/admin/login'
  ];

  for (const route of routes) {
    try {
      const response = await fetch(`${BASE_URL}${route}`, {
        timeout: TIMEOUT
      });

      recordTest(
        `Route: ${route}`,
        response.ok,
        `Status: ${response.status}`
      );

    } catch (error) {
      recordTest(`Route: ${route}`, false, error.message);
    }
  }
}

async function testFileSystem() {
  logSection('📁 File System');
  
  const criticalFiles = [
    '.env.local',
    'package.json',
    'next.config.js',
    'tailwind.config.js',
    'public/images/positive7-logo.png',
    'public/images/placeholder.jpg'
  ];

  for (const file of criticalFiles) {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    recordTest(`File: ${file}`, exists, exists ? 'Exists' : 'Missing');
  }
}

async function generateReport() {
  logSection('📊 Test Summary');
  
  log(`Total Tests: ${results.total}`, 'blue');
  log(`Passed: ${results.passed}`, 'green');
  log(`Failed: ${results.failed}`, 'red');
  log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`, 'yellow');

  if (results.failed > 0) {
    log('\n❌ Failed Tests:', 'red');
    results.tests
      .filter(test => !test.status)
      .forEach(test => {
        log(`   • ${test.name}: ${test.details}`, 'red');
      });
  }

  // Save detailed report
  const reportPath = path.join(process.cwd(), 'functionality-test-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: results.total,
      passed: results.passed,
      failed: results.failed,
      successRate: ((results.passed / results.total) * 100).toFixed(1)
    },
    tests: results.tests,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      baseUrl: BASE_URL
    }
  };

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n📄 Detailed report saved to: ${reportPath}`, 'blue');

  return results.failed === 0;
}

// Main test runner
async function runAllTests() {
  log('🚀 Starting Comprehensive Functionality Tests...', 'bold');
  log(`Base URL: ${BASE_URL}`, 'blue');
  
  try {
    await testEnvironmentVariables();
    await testFileSystem();
    await testSupabaseConnection();
    await testGoogleDriveAPI();
    await testCloudinaryAPI();
    await testAPIEndpoints();
    await testPageRoutes();
    
    const success = await generateReport();
    
    if (success) {
      log('\n🎉 All tests passed! System is healthy.', 'green');
    } else {
      log('\n⚠️ Some tests failed. Check the report for details.', 'yellow');
    }
    
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    log(`\n💥 Test runner failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests };
