{"timestamp": "2025-06-01T06:45:48.249Z", "summary": {"total": 45, "passed": 44, "failed": 1, "successRate": "97.8"}, "tests": [{"name": "NEXT_PUBLIC_SUPABASE_URL", "status": true, "details": "Set"}, {"name": "NEXT_PUBLIC_SUPABASE_ANON_KEY", "status": true, "details": "Set"}, {"name": "SUPABASE_SERVICE_ROLE_KEY", "status": true, "details": "Set"}, {"name": "SUPABASE_JWT_SECRET", "status": true, "details": "Set"}, {"name": "NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME", "status": true, "details": "Set"}, {"name": "NEXT_PUBLIC_CLOUDINARY_API_KEY", "status": true, "details": "Set"}, {"name": "CLOUDINARY_API_SECRET", "status": true, "details": "Set"}, {"name": "GOOGLE_CLIENT_EMAIL", "status": true, "details": "Set"}, {"name": "GOOGLE_PRIVATE_KEY", "status": true, "details": "Set"}, {"name": "EMAIL_USER", "status": true, "details": "Set"}, {"name": "EMAIL_PASSWORD", "status": true, "details": "Set"}, {"name": "File: .env.local", "status": true, "details": "Exists"}, {"name": "File: package.json", "status": true, "details": "Exists"}, {"name": "File: next.config.js", "status": true, "details": "Exists"}, {"name": "File: tailwind.config.js", "status": true, "details": "Exists"}, {"name": "File: public/images/positive7-logo.png", "status": true, "details": "Exists"}, {"name": "File: public/images/placeholder.jpg", "status": true, "details": "Exists"}, {"name": "Database Connection", "status": true, "details": ""}, {"name": "Table: trips", "status": true, "details": ""}, {"name": "Table: blog_posts", "status": true, "details": ""}, {"name": "Table: trip_photos_details", "status": true, "details": ""}, {"name": "Table: team_members", "status": true, "details": ""}, {"name": "Table: inquiries", "status": true, "details": ""}, {"name": "Google Credentials", "status": true, "details": "Present"}, {"name": "Google Drive API", "status": false, "details": "error:1E08010C:DECODER routines::unsupported"}, {"name": "Cloudinary Credentials", "status": true, "details": ""}, {"name": "Cloudinary API Access", "status": true, "details": "Status: 200"}, {"name": "GET /api/trips", "status": true, "details": "Status: 200"}, {"name": "GET /api/blog", "status": true, "details": "Status: 200"}, {"name": "GET /api/inquiries", "status": true, "details": "Status: 200"}, {"name": "GET /api/admin/trips", "status": true, "details": "Status: 200"}, {"name": "GET /api/admin/blogs", "status": true, "details": "Status: 200"}, {"name": "GET /api/admin/trips-photos", "status": true, "details": "Status: 200"}, {"name": "GET /api/admin/team-members", "status": true, "details": "Status: 403"}, {"name": "GET /api/admin/inquiries", "status": true, "details": "Status: 200"}, {"name": "POST /api/cloudinary/upload", "status": true, "details": "Status: 403"}, {"name": "Route: /", "status": true, "details": "Status: 200"}, {"name": "Route: /about", "status": true, "details": "Status: 200"}, {"name": "Route: /trips", "status": true, "details": "Status: 200"}, {"name": "Route: /blog", "status": true, "details": "Status: 200"}, {"name": "Route: /trips-photos", "status": true, "details": "Status: 200"}, {"name": "Route: /contact", "status": true, "details": "Status: 200"}, {"name": "Route: /rural-initiative", "status": true, "details": "Status: 200"}, {"name": "Route: /gallery", "status": true, "details": "Status: 200"}, {"name": "Route: /admin/login", "status": true, "details": "Status: 200"}], "environment": {"nodeVersion": "v22.14.0", "platform": "darwin", "baseUrl": "http://localhost:3000"}}